#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双因子对敲检测算法 - 简化版本
"""

def calculate_hedge_score(profit_a, profit_b, order_amount_a, order_amount_b):
    """
    双因子对敲检测算法
    
    Args:
        profit_a (float): 仓位A的实际盈亏
        profit_b (float): 仓位B的实际盈亏  
        order_amount_a (float): 仓位A的开单金额（真实交易量）
        order_amount_b (float): 仓位B的开单金额（真实交易量）
    
    Returns:
        float: 0.0-1.0，越高越可能是对敲
    """
    
    # 基础检查
    total_profit = profit_a + profit_b
    profit_sum = abs(profit_a) + abs(profit_b)
    order_sum = order_amount_a + order_amount_b
    
    # 特殊情况：总盈亏为0（完全抵消，包括无盈亏情况）
    if abs(total_profit) == 0:
        return 0.8  # 给予较高分数，这也是对敲特征
    
    # 无交易量情况
    if order_sum == 0:
        return 0.0
    
    # 判断是否同边
    is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)
    
    if not is_same_side:
        # ==================== 异边情况：提高判断阈值 ====================
        score = 1.0 - abs(total_profit) / profit_sum
        # 提高阈值，减少误报
        return max(0, (score - 0.2) / 0.8)  # 将[0.2,1.0]映射到[0,1.0]
    
    else:
        # ==================== 同边情况：双因子检测 ====================
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        min_abs = min(abs_a, abs_b)
        
        # 使用真实交易量作为规模
        scale = order_sum
        
        # ---------- 因子1：接近0程度评分（基于相对比例） ----------
        min_relative = min_abs / scale
        if min_relative <= 0.001:      # ≤0.1%，几乎无盈亏
            closeness_score = 0.95
        elif min_relative <= 0.003:    # ≤0.3%，很接近0
            closeness_score = 0.9
        elif min_relative <= 0.005:    # ≤0.5%，接近0
            closeness_score = 0.8
        elif min_relative <= 0.01:     # ≤1%，比较接近0
            closeness_score = 0.6
        elif min_relative <= 0.02:     # ≤2%，稍微接近0
            closeness_score = 0.4
        elif min_relative <= 0.05:     # ≤5%，不太接近0
            closeness_score = 0.2
        else:                          # >5%，远离0
            closeness_score = 0.1
        
        # ---------- 因子2：波动率比值评分 ----------
        rate_a = abs_a / order_amount_a if order_amount_a > 0 else 0
        rate_b = abs_b / order_amount_b if order_amount_b > 0 else 0
        
        min_rate = min(rate_a, rate_b)
        max_rate = max(rate_a, rate_b)
        
        # 比值越小，差异越大，越可能是对敲
        if max_rate > 0:
            rate_ratio = min_rate / max_rate
            
            if rate_ratio <= 0.1:        # 差异巨大（小方是大方的10%以下）
                volatility_score = 0.9
            elif rate_ratio <= 0.2:      # 差异很大（小方是大方的20%以下）
                volatility_score = 0.8
            elif rate_ratio <= 0.5:      # 差异较大（小方是大方的50%以下）
                volatility_score = 0.6
            elif rate_ratio <= 0.8:      # 差异一般（小方是大方的80%以下）
                volatility_score = 0.4
            else:                        # 差异很小（比值>0.8）
                volatility_score = 0.2
        else:
            volatility_score = 0.5
        
        # ---------- 双因子加权计算 ----------
        # 接近0程度(60%) + 波动率差异(40%)
        score = 0.6 * closeness_score + 0.4 * volatility_score
        
        return score


# ==================== 使用示例 ====================
if __name__ == "__main__":
    # 测试用例
    test_cases = [
        # (profit_a, profit_b, order_a, order_b, 描述)
        (-0.03, -1.0, 100, 100, "同边对敲：一方接近0"),
        (10, -9.5, 100, 100, "异边对敲：接近抵消"),
        (-5, -8, 100, 100, "正常同边交易：都有正常亏损"),
        (-0.1, -5, 1000, 1000, "大额同边对敲"),
        (15, 20, 100, 100, "正常同边盈利"),
        (5, -5, 100, 100, "完全抵消：总盈亏为0"),
        (0, 0, 100, 100, "无盈亏：都为0"),
        # 新增测试用例
        (-2500, -50000, 500000, 500000, "大额同边对敲：一方0.5%，另一方10%"),
        (1000, -50000, 100000, 500000, "大额异边：不完全抵消"),
        (-1, -2, 100, 100, "同边小差异：1% vs 2%"),
        (-0.1, -10, 100, 100, "同边大差异：0.1% vs 10%"),
    ]
    
    print("双因子对敲检测算法测试结果：")
    print("-" * 70)
    
    for profit_a, profit_b, order_a, order_b, desc in test_cases:
        score = calculate_hedge_score(profit_a, profit_b, order_a, order_b)
        
        # 计算详细信息用于分析
        abs_a = abs(profit_a)
        abs_b = abs(profit_b)
        scale = order_a + order_b
        min_relative = min(abs_a, abs_b) / scale if scale > 0 else 0
        
        rate_a = abs_a / order_a if order_a > 0 else 0
        rate_b = abs_b / order_b if order_b > 0 else 0
        min_rate = min(rate_a, rate_b)
        max_rate = max(rate_a, rate_b)
        rate_ratio = min_rate / max_rate if max_rate > 0 else 1
        
        print(f"{desc}")
        print(f"  盈亏: {profit_a:+.2f} / {profit_b:+.2f}")
        print(f"  开单: {order_a} / {order_b}")
        print(f"  评分: {score:.3f}")
        print(f"  详情: 最小相对值={min_relative:.4f}, 比值={rate_ratio:.3f}")
        print()
